import React, { useState, useEffect, useRef } from 'react';
import './App.css';

function App() {
  const [sessionId, setSessionId] = useState(null);
  const [connected, setConnected] = useState(false);
  const [instruction, setInstruction] = useState('');
  const [logs, setLogs] = useState([]);
  const [browserState, setBrowserState] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const wsRef = useRef(null);
  const logsEndRef = useRef(null);

  // Auto-scroll logs
  useEffect(() => {
    if (logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs]);

  const startSession = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8000/start-session', {
        method: 'POST',
      });
      
      if (!response.ok) {
        throw new Error('Failed to start browser session');
      }
      
      const data = await response.json();
      setSessionId(data.session_id);
      connectWebSocket(data.session_id);
      addLog('System', `Session started with ID: ${data.session_id}`);
    } catch (err) {
      setError(err.message);
      addLog('Error', err.message);
    } finally {
      setLoading(false);
    }
  };

  const connectWebSocket = (id) => {
    const ws = new WebSocket(`ws://localhost:8000/ws/${id}`);
    
    ws.onopen = () => {
      setConnected(true);
      addLog('System', 'WebSocket connected');
    };
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      if (data.type === 'state_update') {
        setBrowserState(data.data);
      } else if (data.type === 'instruction_result') {
        addLog('Result', JSON.stringify(data.data));
      } else if (data.type === 'error') {
        addLog('Error', data.message);
      }
    };
    
    ws.onclose = () => {
      setConnected(false);
      addLog('System', 'WebSocket disconnected');
    };
    
    ws.onerror = (error) => {
      setError('WebSocket error');
      addLog('Error', 'WebSocket error');
    };
    
    wsRef.current = ws;
  };

  const sendInstruction = () => {
    if (!wsRef.current || !instruction.trim()) return;
    
    wsRef.current.send(JSON.stringify({ command: instruction }));
    addLog('Instruction', instruction);
    setInstruction('');
  };

  const addLog = (type, message) => {
    setLogs(prev => [...prev, { type, message, timestamp: new Date().toISOString() }]);
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>Autonomous Browser Agent</h1>
      </header>
      
      <main>
        <div className="control-panel">
          {!sessionId ? (
            <button 
              onClick={startSession} 
              disabled={loading}
              className="connect-button"
            >
              {loading ? 'Connecting...' : 'Connect Agent'}
            </button>
          ) : (
            <div className="instruction-panel">
              <input
                type="text"
                value={instruction}
                onChange={(e) => setInstruction(e.target.value)}
                placeholder="Enter instruction (e.g., 'go to google.com')"
                disabled={!connected}
                onKeyPress={(e) => e.key === 'Enter' && sendInstruction()}
              />
              <button 
                onClick={sendInstruction}
                disabled={!connected || !instruction.trim()}
              >
                Send
              </button>
            </div>
          )}
        </div>
        
        <div className="status-panel">
          <div className="connection-status">
            Status: {connected ? 'Connected' : 'Disconnected'}
          </div>
          
          {browserState.url && (
            <div className="browser-info">
              <p>Current URL: {browserState.url}</p>
              <p>Browser Status: {browserState.status}</p>
            </div>
          )}
        </div>
        
        <div className="logs-panel">
          <h3>Activity Log</h3>
          <div className="logs">
            {logs.map((log, index) => (
              <div key={index} className={`log-entry log-${log.type.toLowerCase()}`}>
                <span className="log-time">
                  {new Date(log.timestamp).toLocaleTimeString()}
                </span>
                <span className="log-type">[{log.type}]</span>
                <span className="log-message">{log.message}</span>
              </div>
            ))}
            <div ref={logsEndRef} />
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;
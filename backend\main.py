import os
import logging
import re
from datetime import datetime
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
from browserbase import Browserbase
import json
import asyncio
from typing import Dict, List, Optional, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
import openai
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(title="Autonomous Browser Agent", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Browserbase client
browserbase = Browserbase(
    api_key=os.getenv("BROWSERBASE_API_KEY", "bb_live_kJiB2rS65fUqy3iAQSi1gpnOYQY")
)

# Initialize OpenAI client (optional for advanced reasoning)
openai_client = None
if os.getenv("OPENAI_API_KEY"):
    openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Data models
class SessionInfo(BaseModel):
    id: str
    status: str
    url: str = ""
    title: str = ""
    created_at: datetime
    browser_context: Optional[Any] = None
    page: Optional[Any] = None

# Store active connections and sessions
active_connections: Dict[str, WebSocket] = {}
browser_sessions: Dict[str, SessionInfo] = {}
playwright_instances: Dict[str, Any] = {}

@app.get("/")
async def root():
    return {"message": "Autonomous Browser Agent API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "active_sessions": len(browser_sessions)}

@app.post("/start-session")
async def start_session():
    try:
        logger.info("Creating new browser session...")

        # Create a new browser session using Browserbase
        session = browserbase.sessions.create(
            project_id=os.getenv("BROWSERBASE_PROJECT_ID", "dfc1c230-cc04-4259-bbb9-4e751742ecd6")
        )

        session_id = session.id
        logger.info(f"Created session: {session_id}")

        # Store session info
        session_info = SessionInfo(
            id=session_id,
            status="created",
            created_at=datetime.now()
        )
        browser_sessions[session_id] = session_info

        return {
            "session_id": session_id,
            "status": "created",
            "connect_url": session.connect_url
        }
    except Exception as e:
        logger.error(f"Failed to create session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    await websocket.accept()
    active_connections[session_id] = websocket

    try:
        logger.info(f"WebSocket connected for session: {session_id}")

        # Initialize Playwright connection if not exists
        if session_id not in playwright_instances:
            await initialize_playwright_connection(session_id, websocket)

        # Start monitoring browser state
        asyncio.create_task(monitor_browser_state(session_id, websocket))

        # Send initial connection success message
        await websocket.send_json({
            "type": "connection_established",
            "data": {"session_id": session_id, "status": "connected"}
        })

        # Process incoming messages
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)

            if "command" in message:
                # Execute the instruction with enhanced reasoning
                await websocket.send_json({
                    "type": "instruction_received",
                    "data": {"instruction": message["command"]}
                })

                result = await execute_instruction_with_reasoning(session_id, message["command"], websocket)
                await websocket.send_json({"type": "instruction_result", "data": result})

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for session: {session_id}")
    except Exception as e:
        logger.error(f"WebSocket error for session {session_id}: {str(e)}")
        await websocket.send_json({"type": "error", "message": str(e)})
    finally:
        if session_id in active_connections:
            del active_connections[session_id]
        await cleanup_session(session_id)

async def initialize_playwright_connection(session_id: str, websocket: WebSocket):
    """Initialize Playwright connection to Browserbase session."""
    try:
        from playwright.async_api import async_playwright

        # Get session info from Browserbase
        session = browserbase.sessions.retrieve(session_id)

        # Initialize Playwright
        playwright = await async_playwright().start()
        browser = await playwright.chromium.connect_over_cdp(session.connect_url)

        # Get the default context and page
        context = browser.contexts[0] if browser.contexts else await browser.new_context()
        page = await context.new_page() if not context.pages else context.pages[0]

        # Store instances
        playwright_instances[session_id] = {
            "playwright": playwright,
            "browser": browser,
            "context": context,
            "page": page
        }

        # Update session info
        if session_id in browser_sessions:
            browser_sessions[session_id].status = "connected"
            browser_sessions[session_id].browser_context = context
            browser_sessions[session_id].page = page

        logger.info(f"Playwright connected for session: {session_id}")

    except Exception as e:
        logger.error(f"Failed to initialize Playwright for session {session_id}: {str(e)}")
        await websocket.send_json({"type": "error", "message": f"Failed to connect to browser: {str(e)}"})

async def cleanup_session(session_id: str):
    """Clean up session resources."""
    try:
        if session_id in playwright_instances:
            instances = playwright_instances[session_id]
            if "browser" in instances:
                await instances["browser"].close()
            if "playwright" in instances:
                await instances["playwright"].stop()
            del playwright_instances[session_id]

        if session_id in browser_sessions:
            browser_sessions[session_id].status = "closed"

        logger.info(f"Cleaned up session: {session_id}")
    except Exception as e:
        logger.error(f"Error cleaning up session {session_id}: {str(e)}")

async def monitor_browser_state(session_id: str, websocket: WebSocket):
    """Monitor browser state and send updates to the client."""
    while session_id in active_connections:
        try:
            if session_id in playwright_instances:
                page = playwright_instances[session_id].get("page")
                if page:
                    try:
                        url = page.url
                        title = await page.title()

                        # Update session info
                        if session_id in browser_sessions:
                            browser_sessions[session_id].url = url
                            browser_sessions[session_id].title = title

                        await websocket.send_json({
                            "type": "state_update",
                            "data": {
                                "url": url,
                                "title": title,
                                "status": "active",
                                "session_id": session_id
                            }
                        })
                    except Exception as page_error:
                        logger.debug(f"Page state error for session {session_id}: {str(page_error)}")

        except Exception as e:
            logger.error(f"Monitor error for session {session_id}: {str(e)}")

        await asyncio.sleep(2)  # Poll every 2 seconds

async def execute_instruction_with_reasoning(session_id: str, instruction: str, websocket: WebSocket) -> dict:
    """Execute instruction with enhanced reasoning and step-by-step execution."""
    try:
        logger.info(f"Executing instruction for session {session_id}: {instruction}")

        # Send reasoning update
        await websocket.send_json({
            "type": "reasoning_step",
            "data": {"step": "analyzing", "message": f"Analyzing instruction: {instruction}"}
        })

        # Get page instance
        if session_id not in playwright_instances:
            return {"status": "error", "message": "Browser session not connected"}

        page = playwright_instances[session_id].get("page")
        if not page:
            return {"status": "error", "message": "No active page found"}

        # Use AI reasoning if available
        if openai_client:
            steps = await get_ai_reasoning(instruction, page)
            for step in steps:
                await websocket.send_json({
                    "type": "reasoning_step",
                    "data": {"step": "executing", "message": step}
                })
                result = await execute_single_step(session_id, step, page)
                if result.get("status") == "error":
                    return result
        else:
            # Fallback to basic instruction parsing
            result = await execute_basic_instruction(session_id, instruction, page)
            return result

        return {"status": "success", "message": "Instruction completed successfully"}

    except Exception as e:
        logger.error(f"Error executing instruction: {str(e)}")
        return {"status": "error", "message": str(e)}

async def get_ai_reasoning(instruction: str, page) -> List[str]:
    """Use AI to break down complex instructions into steps."""
    try:
        current_url = page.url
        current_title = await page.title()

        prompt = f"""
        You are a browser automation assistant. Break down this instruction into simple, actionable steps:

        Instruction: {instruction}
        Current page: {current_title} ({current_url})

        Return a list of simple browser actions like:
        - "navigate to [url]"
        - "click [element description]"
        - "type [text] into [field description]"
        - "scroll down"
        - "wait for [element]"
        - "extract text from [element]"

        Keep steps simple and specific. Return only the action steps, one per line.
        """

        response = openai_client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=200,
            temperature=0.1
        )

        steps = response.choices[0].message.content.strip().split('\n')
        return [step.strip('- ').strip() for step in steps if step.strip()]

    except Exception as e:
        logger.error(f"AI reasoning error: {str(e)}")
        return [instruction]  # Fallback to original instruction

async def execute_single_step(session_id: str, step: str, page) -> dict:
    """Execute a single browser action step."""
    try:
        step = step.lower().strip()

        # Navigation
        if step.startswith("navigate to ") or step.startswith("go to "):
            url = step.split(" to ", 1)[1].strip()
            if not url.startswith(("http://", "https://")):
                url = "https://" + url
            await page.goto(url)
            return {"status": "success", "action": "navigate", "url": url}

        # Clicking
        elif step.startswith("click "):
            element_desc = step[6:].strip()
            # Try multiple selectors
            selectors = [
                f"text={element_desc}",
                f"[aria-label*='{element_desc}']",
                f"[title*='{element_desc}']",
                f"button:has-text('{element_desc}')",
                f"a:has-text('{element_desc}')"
            ]

            for selector in selectors:
                try:
                    await page.click(selector, timeout=5000)
                    return {"status": "success", "action": "click", "element": element_desc}
                except:
                    continue

            return {"status": "error", "message": f"Could not find element to click: {element_desc}"}

        # Typing
        elif "type " in step and " into " in step:
            parts = step.split(" into ")
            if len(parts) == 2:
                text_part = parts[0].strip()
                field_desc = parts[1].strip()

                if text_part.startswith("type "):
                    text = text_part[5:].strip()

                    # Try multiple field selectors
                    selectors = [
                        f"input[placeholder*='{field_desc}']",
                        f"input[name*='{field_desc}']",
                        f"textarea[placeholder*='{field_desc}']",
                        f"[aria-label*='{field_desc}']"
                    ]

                    for selector in selectors:
                        try:
                            await page.fill(selector, text)
                            return {"status": "success", "action": "type", "text": text, "field": field_desc}
                        except:
                            continue

                    return {"status": "error", "message": f"Could not find field: {field_desc}"}

        # Scrolling
        elif "scroll" in step:
            if "down" in step:
                await page.keyboard.press("PageDown")
            elif "up" in step:
                await page.keyboard.press("PageUp")
            else:
                await page.mouse.wheel(0, 500)
            return {"status": "success", "action": "scroll"}

        # Waiting
        elif step.startswith("wait"):
            await asyncio.sleep(2)
            return {"status": "success", "action": "wait"}

        # Text extraction
        elif step.startswith("extract ") or step.startswith("get "):
            element_desc = step.split(" ", 1)[1].strip()
            try:
                text = await page.text_content(f"text={element_desc}")
                return {"status": "success", "action": "extract", "text": text}
            except:
                return {"status": "error", "message": f"Could not extract text from: {element_desc}"}

        return {"status": "error", "message": f"Unknown step: {step}"}

    except Exception as e:
        return {"status": "error", "message": str(e)}

async def execute_basic_instruction(session_id: str, instruction: str, page) -> dict:
    """Basic instruction execution without AI reasoning."""
    return await execute_single_step(session_id, instruction, page)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
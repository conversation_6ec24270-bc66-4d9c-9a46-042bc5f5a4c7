import os
from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
from browserbase import BrowserBase
import json
import asyncio
from typing import Dict, List, Optional

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Browserbase client
browserbase = BrowserBase(
    project_id=os.getenv("BROWSERBASE_PROJECT_ID", "dfc1c230-cc04-4259-bbb9-4e751742ecd6"),
    api_key=os.getenv("BROWSERBASE_API_KEY", "bb_live_kJiB2rS65fUqy3iAQSi1gpnOYQY")
)

# Store active connections
active_connections: Dict[str, WebSocket] = {}
# Store browser sessions
browser_sessions: Dict[str, dict] = {}

@app.get("/")
async def root():
    return {"message": "Autonomous Browser Agent API"}

@app.post("/start-session")
async def start_session():
    try:
        # Create a new browser session
        session = await browserbase.create_session()
        session_id = session["id"]
        browser_sessions[session_id] = session
        return {"session_id": session_id, "status": "created"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    await websocket.accept()
    active_connections[session_id] = websocket
    
    try:
        # Start monitoring browser state
        monitor_task = asyncio.create_task(monitor_browser_state(session_id, websocket))
        
        # Process incoming messages
        while True:
            data = await websocket.receive_text()
            instruction = json.loads(data)
            
            # Execute the instruction
            result = await execute_instruction(session_id, instruction["command"])
            await websocket.send_json({"type": "instruction_result", "data": result})
            
    except WebSocketDisconnect:
        if session_id in active_connections:
            del active_connections[session_id]
        # Optionally close the browser session here
    except Exception as e:
        await websocket.send_json({"type": "error", "message": str(e)})
    finally:
        if session_id in active_connections:
            del active_connections[session_id]

async def monitor_browser_state(session_id: str, websocket: WebSocket):
    """Monitor browser state and send updates to the client."""
    while session_id in active_connections:
        try:
            session = await browserbase.get_session(session_id)
            await websocket.send_json({"type": "state_update", "data": session})
        except Exception as e:
            await websocket.send_json({"type": "error", "message": str(e)})
        await asyncio.sleep(1)  # Poll every second

async def execute_instruction(session_id: str, instruction: str) -> dict:
    """Parse and execute a natural language instruction."""
    instruction = instruction.lower().strip()
    
    # Navigation commands
    if instruction.startswith("go to "):
        url = instruction[6:].strip()
        if not url.startswith(("http://", "https://")):
            url = "https://" + url
        return await browserbase.navigate(session_id, url)
    
    # Click commands
    elif instruction.startswith("click "):
        element = instruction[6:].strip()
        return await browserbase.click(session_id, {"text": element})
    
    # Type commands
    elif "type " in instruction and " into " in instruction:
        parts = instruction.split(" into ")
        if len(parts) == 2:
            text_part = parts[0].strip()
            field_part = parts[1].strip()
            
            if text_part.startswith("type "):
                text = text_part[5:].strip()
                return await browserbase.type(session_id, {"text": field_part}, text)
    
    # Scrape text
    elif instruction.startswith("scrape ") or instruction.startswith("extract "):
        element = instruction.split(" ", 1)[1].strip()
        return await browserbase.get_text(session_id, {"text": element})
    
    # Default response for unrecognized commands
    return {"status": "error", "message": f"Could not understand instruction: {instruction}"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
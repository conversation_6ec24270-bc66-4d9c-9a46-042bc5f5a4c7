# Autonomous Browser Agent

A complete autonomous browser agent using Browserbase that can interact with web pages through natural language instructions with AI-powered reasoning capabilities.

## 🚀 Features

- **AI-Powered Reasoning**: Advanced instruction interpretation using OpenAI (optional)
- **Real-time Browser Automation**: Natural language commands executed in real-time
- **WebSocket Communication**: Live updates and status monitoring
- **Modern React Frontend**: Beautiful, responsive UI with real-time feedback
- **FastAPI Backend**: High-performance backend with Browserbase integration
- **Comprehensive Browser Actions**: Navigate, click, type, scroll, extract data, and more
- **Session Management**: Persistent browser sessions with proper cleanup
- **Error Recovery**: Robust error handling and recovery mechanisms
- **Live Monitoring**: Real-time browser state updates and activity logs

## 🏗️ Architecture

```
┌─────────────────┐    WebSocket    ┌──────────────────┐    Playwright    ┌─────────────────┐
│   React Frontend│◄──────────────►│  FastAPI Backend │◄────────────────►│   Browserbase   │
│                 │                 │                  │                  │   Cloud Browser │
└─────────────────┘                 └──────────────────┘                  └─────────────────┘
                                             │
                                             ▼
                                    ┌──────────────────┐
                                    │   OpenAI API     │
                                    │   (Optional)     │
                                    └──────────────────┘
```

## 📋 Prerequisites

- **Python 3.8+**
- **Node.js 14+**
- **npm or yarn**
- **Browserbase Account** (API key provided)
- **OpenAI API Key** (optional, for enhanced reasoning)

## 🛠️ Setup

### 1. Environment Configuration

Copy the example environment file and configure your credentials:

```bash
cp .env.example .env
```

Edit `.env` with your credentials:
```env
BROWSERBASE_PROJECT_ID=dfc1c230-cc04-4259-bbb9-4e751742ecd6
BROWSERBASE_API_KEY=bb_live_kJiB2rS65fUqy3iAQSi1gpnOYQY
# OPENAI_API_KEY=your_openai_api_key_here  # Optional for AI reasoning
```

### 2. Backend Setup

Install Python dependencies:
```bash
pip install -r requirements.txt
```

Install Playwright browsers:
```bash
playwright install chromium
```

Start the backend server:
```bash
cd backend
python main.py
```

Or using uvicorn:
```bash
uvicorn backend.main:app --reload --host 0.0.0.0 --port 8000
```

### 3. Frontend Setup

Navigate to the frontend directory and install dependencies:
```bash
cd frontend
npm install
```

Start the development server:
```bash
npm start
```

The frontend will be available at `http://localhost:3000`

## 🎯 Usage

### Basic Usage

1. **Start a Session**: Open `http://localhost:3000` and click "Connect Agent"
2. **Send Instructions**: Enter natural language commands in the input field
3. **Monitor Progress**: Watch real-time updates in the activity log and status panel

### Example Commands

#### Navigation
- `"go to google.com"`
- `"navigate to https://example.com"`
- `"visit reddit.com"`

#### Interaction
- `"click the search button"`
- `"click on login"`
- `"type hello world into the search box"`
- `"fill the email <NAME_EMAIL>"`

#### Advanced Actions
- `"scroll down"`
- `"wait for the page to load"`
- `"extract text from the title"`
- `"take a screenshot"`

#### Complex Instructions (with AI reasoning)
- `"search for python tutorials on google"`
- `"login to the website with my credentials"`
- `"find and click the download button"`

## 🧠 AI Reasoning (Optional)

When OpenAI API key is configured, the agent can:

- **Break down complex instructions** into simple steps
- **Understand context** from the current page
- **Adapt to different websites** automatically
- **Provide detailed reasoning** for each action

Example with AI reasoning:
```
Instruction: "search for python tutorials on google"

AI Reasoning Steps:
1. Navigate to google.com
2. Click on the search input field
3. Type "python tutorials"
4. Click the search button
5. Wait for results to load
```

## 🔧 Supported Browser Actions

| Action Type | Examples | Description |
|-------------|----------|-------------|
| **Navigation** | `go to [url]`, `navigate to [url]` | Navigate to web pages |
| **Clicking** | `click [element]`, `click on [button]` | Click on page elements |
| **Typing** | `type [text] into [field]` | Fill form fields |
| **Scrolling** | `scroll down`, `scroll up` | Scroll page content |
| **Waiting** | `wait`, `wait for [element]` | Wait for page loads |
| **Extraction** | `extract text from [element]` | Get text content |
| **Screenshots** | `take a screenshot` | Capture page images |

## 📊 API Endpoints

### REST API
- `GET /` - API status and version
- `GET /health` - Health check and active sessions count
- `POST /start-session` - Create new browser session

### WebSocket
- `WS /ws/{session_id}` - Real-time communication with browser session

### Message Types
- `connection_established` - Session connected successfully
- `state_update` - Browser state changes (URL, title, etc.)
- `instruction_received` - Instruction acknowledged
- `reasoning_step` - AI reasoning progress
- `instruction_result` - Execution result
- `error` - Error messages

## 🔒 Security & Production Notes

### Security Considerations
- **API Keys**: Store Browserbase and OpenAI API keys securely
- **CORS**: Configure appropriate CORS settings for production
- **Authentication**: Implement user authentication for production use
- **Rate Limiting**: Add rate limiting to prevent abuse
- **Input Validation**: Validate and sanitize user inputs

### Production Deployment
- Use environment variables for all sensitive configuration
- Implement proper logging and monitoring
- Set up SSL/TLS certificates
- Configure reverse proxy (nginx/Apache)
- Use process managers (PM2, systemd)

## 🐛 Troubleshooting

### Common Issues

**Backend won't start:**
- Check Python version (3.8+ required)
- Verify all dependencies are installed: `pip install -r requirements.txt`
- Install Playwright browsers: `playwright install chromium`

**Frontend connection issues:**
- Ensure backend is running on port 8000
- Check CORS configuration
- Verify WebSocket connection in browser dev tools

**Browser session errors:**
- Verify Browserbase API key and project ID
- Check Browserbase account limits and usage
- Ensure stable internet connection

**AI reasoning not working:**
- Verify OpenAI API key is set in `.env`
- Check OpenAI account credits and limits
- Review API usage in OpenAI dashboard

### Debug Mode

Enable debug logging by setting in `.env`:
```env
DEBUG=True
LOG_LEVEL=DEBUG
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and add tests
4. Commit your changes: `git commit -am 'Add feature'`
5. Push to the branch: `git push origin feature-name`
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Browserbase** for providing cloud browser infrastructure
- **OpenAI** for AI reasoning capabilities
- **Playwright** for browser automation
- **FastAPI** for the high-performance backend
- **React** for the modern frontend framework
#!/usr/bin/env python3
"""
Test script to verify Browserbase connection
"""

import requests
import json

def test_browserbase_direct():
    """Test direct connection to Browserbase API"""
    print("🧪 Testing direct Browserbase API connection...")
    
    headers = {
        'X-BB-API-Key': 'bb_live_kJiB2rS65fUqy3iAQSi1gpnOYQY',
        'Content-Type': 'application/json'
    }
    
    data = {
        'projectId': 'dfc1c230-cc04-4259-bbb9-4e751742ecd6'
    }
    
    try:
        response = requests.post(
            'https://api.browserbase.com/v1/sessions',
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 201:
            session_data = response.json()
            print(f"✅ Success! Session ID: {session_data.get('id')}")
            print(f"Connect URL: {session_data.get('connectUrl')}")
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_backend_api():
    """Test our backend API"""
    print("\n🧪 Testing backend API connection...")
    
    try:
        response = requests.post(
            'http://localhost:8000/start-session',
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Backend API working!")
            return True
        else:
            print(f"❌ Backend API error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Backend API exception: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Browserbase Connection Test")
    print("=" * 50)
    
    # Test direct API
    direct_success = test_browserbase_direct()
    
    # Test backend API
    backend_success = test_backend_api()
    
    print("\n" + "=" * 50)
    print("📊 Results:")
    print(f"Direct API: {'✅ PASS' if direct_success else '❌ FAIL'}")
    print(f"Backend API: {'✅ PASS' if backend_success else '❌ FAIL'}")
    
    if direct_success and backend_success:
        print("\n🎉 All tests passed! Connection should work.")
    elif direct_success:
        print("\n⚠️ Direct API works, but backend has issues.")
    else:
        print("\n❌ API connection failed. Check credentials.")

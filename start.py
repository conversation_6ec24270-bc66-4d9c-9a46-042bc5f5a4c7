#!/usr/bin/env python3
"""
Startup script for the Autonomous Browser Agent
This script helps start both backend and frontend services
"""

import subprocess
import sys
import os
import time
import webbrowser
from pathlib import Path

def check_python_version():
    """Check if Python version is 3.8+"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        sys.exit(1)
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")

def check_node():
    """Check if Node.js is installed"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js {result.stdout.strip()} detected")
            return True
    except FileNotFoundError:
        pass
    print("❌ Node.js not found. Please install Node.js 14+")
    return False

def install_python_deps():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], check=True)
        print("✅ Python dependencies installed")
    except subprocess.CalledProcessError:
        print("❌ Failed to install Python dependencies")
        return False
    return True

def install_playwright():
    """Install Playwright browsers"""
    print("🎭 Installing Playwright browsers...")
    try:
        subprocess.run([sys.executable, '-m', 'playwright', 'install', 'chromium'], check=True)
        print("✅ Playwright browsers installed")
    except subprocess.CalledProcessError:
        print("❌ Failed to install Playwright browsers")
        return False
    return True

def install_node_deps():
    """Install Node.js dependencies"""
    print("📦 Installing Node.js dependencies...")
    frontend_path = Path("frontend")
    if not frontend_path.exists():
        print("❌ Frontend directory not found")
        return False
    
    try:
        subprocess.run(['npm', 'install'], cwd=frontend_path, check=True)
        print("✅ Node.js dependencies installed")
    except subprocess.CalledProcessError:
        print("❌ Failed to install Node.js dependencies")
        return False
    return True

def start_backend():
    """Start the backend server"""
    print("🚀 Starting backend server...")
    try:
        # Start backend in background
        backend_process = subprocess.Popen([
            sys.executable, '-m', 'uvicorn', 
            'backend.main:app', 
            '--reload', 
            '--host', '0.0.0.0', 
            '--port', '8000'
        ])
        print("✅ Backend server started on http://localhost:8000")
        return backend_process
    except Exception as e:
        print(f"❌ Failed to start backend: {e}")
        return None

def start_frontend():
    """Start the frontend server"""
    print("🚀 Starting frontend server...")
    frontend_path = Path("frontend")
    try:
        # Start frontend in background
        frontend_process = subprocess.Popen(['npm', 'start'], cwd=frontend_path)
        print("✅ Frontend server starting on http://localhost:3000")
        return frontend_process
    except Exception as e:
        print(f"❌ Failed to start frontend: {e}")
        return None

def main():
    """Main startup function"""
    print("🤖 Autonomous Browser Agent Startup Script")
    print("=" * 50)
    
    # Check prerequisites
    check_python_version()
    if not check_node():
        sys.exit(1)
    
    # Check if .env file exists
    if not Path(".env").exists():
        print("⚠️  .env file not found. Creating from template...")
        if Path(".env.example").exists():
            import shutil
            shutil.copy(".env.example", ".env")
            print("✅ .env file created. Please edit it with your credentials.")
        else:
            print("❌ .env.example not found")
    
    # Install dependencies
    if not install_python_deps():
        sys.exit(1)
    
    if not install_playwright():
        sys.exit(1)
    
    if not install_node_deps():
        sys.exit(1)
    
    print("\n🎉 All dependencies installed successfully!")
    print("=" * 50)
    
    # Start services
    backend_process = start_backend()
    if not backend_process:
        sys.exit(1)
    
    # Wait a bit for backend to start
    print("⏳ Waiting for backend to initialize...")
    time.sleep(3)
    
    frontend_process = start_frontend()
    if not frontend_process:
        backend_process.terminate()
        sys.exit(1)
    
    # Wait a bit for frontend to start
    print("⏳ Waiting for frontend to initialize...")
    time.sleep(5)
    
    # Open browser
    print("🌐 Opening browser...")
    webbrowser.open("http://localhost:3000")
    
    print("\n✅ Autonomous Browser Agent is running!")
    print("📊 Backend API: http://localhost:8000")
    print("🖥️  Frontend UI: http://localhost:3000")
    print("\nPress Ctrl+C to stop all services")
    
    try:
        # Wait for user to stop
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Stopping services...")
        if frontend_process:
            frontend_process.terminate()
        if backend_process:
            backend_process.terminate()
        print("✅ All services stopped")

if __name__ == "__main__":
    main()

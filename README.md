# Autonomous Browser Agent

A complete autonomous browser agent using Browserbase that can interact with web pages through natural language instructions.

## Features

- Real-time browser automation through natural language commands
- WebSocket-based communication for live updates
- Modern React frontend with real-time status display
- FastAPI backend with Browserbase integration

## Setup

### Prerequisites

- Python 3.8+
- Node.js 14+
- npm or yarn

### Backend Setup

1. Create a `.env` file in the root directory with your Browserbase credentials:
```
BROWSERBASE_PROJECT_ID=your_project_id
BROWSERBASE_API_KEY=your_api_key
```

2. Install Python dependencies:
```bash
pip install -r requirements.txt
```

3. Start the backend server:
```bash
uvicorn backend.main:app --reload
```

### Frontend Setup

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

## Usage

1. Open your browser and navigate to `http://localhost:3000`
2. Click "Connect Agent" to start a new browser session
3. Enter natural language instructions in the input field, such as:
   - "go to google.com"
   - "click search"
   - "type hello world into search box"

## Supported Commands

The agent currently supports the following types of instructions:

- Navigation: "go to [url]"
- Clicking: "click [element]"
- Typing: "type [text] into [field]"

More commands can be added by extending the `execute_instruction` method in the backend.

## Architecture

- Backend: FastAPI with WebSocket support and Browserbase integration
- Frontend: React with TypeScript and Tailwind CSS
- Communication: WebSocket-based real-time updates
- Browser Automation: Browserbase API

## Security Notes

- Store your Browserbase credentials securely
- In production, implement proper authentication and rate limiting
- Configure CORS settings appropriately for your deployment environment 
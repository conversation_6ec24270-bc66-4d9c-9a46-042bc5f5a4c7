#!/usr/bin/env python3
"""
Test script to verify the Autonomous Browser Agent setup
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# Add the backend directory to the path
sys.path.append(str(Path(__file__).parent / "backend"))

async def test_browserbase_connection():
    """Test Browserbase connection"""
    print("🧪 Testing Browserbase connection...")
    
    try:
        from browserbase import Browserbase
        
        # Initialize client
        browserbase = Browserbase(
            api_key=os.getenv("BROWSERBASE_API_KEY", "bb_live_kJiB2rS65fUqy3iAQSi1gpnOYQY")
        )
        
        # Create a test session
        session = browserbase.sessions.create(
            project_id=os.getenv("BROWSERBASE_PROJECT_ID", "dfc1c230-cc04-4259-bbb9-4e751742ecd6")
        )
        
        print(f"✅ Browserbase connection successful!")
        print(f"   Session ID: {session.id}")
        print(f"   Connect URL: {session.connect_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Browserbase connection failed: {e}")
        return False

async def test_playwright_connection():
    """Test Playwright connection to Browserbase"""
    print("🎭 Testing Playwright connection...")
    
    try:
        from playwright.async_api import async_playwright
        from browserbase import Browserbase
        
        # Initialize Browserbase
        browserbase = Browserbase(
            api_key=os.getenv("BROWSERBASE_API_KEY", "bb_live_kJiB2rS65fUqy3iAQSi1gpnOYQY")
        )
        
        # Create session
        session = browserbase.sessions.create(
            project_id=os.getenv("BROWSERBASE_PROJECT_ID", "dfc1c230-cc04-4259-bbb9-4e751742ecd6")
        )
        
        # Connect with Playwright
        playwright = await async_playwright().start()
        browser = await playwright.chromium.connect_over_cdp(session.connect_url)
        
        # Get page
        context = browser.contexts[0] if browser.contexts else await browser.new_context()
        page = await context.new_page() if not context.pages else context.pages[0]
        
        # Test navigation
        await page.goto("https://example.com")
        title = await page.title()
        
        print(f"✅ Playwright connection successful!")
        print(f"   Page title: {title}")
        
        # Cleanup
        await browser.close()
        await playwright.stop()
        
        return True
        
    except Exception as e:
        print(f"❌ Playwright connection failed: {e}")
        return False

def test_openai_connection():
    """Test OpenAI connection (optional)"""
    print("🤖 Testing OpenAI connection...")
    
    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key:
        print("⚠️  OpenAI API key not found (optional)")
        return True
    
    try:
        import openai
        
        client = openai.OpenAI(api_key=openai_key)
        
        # Test with a simple completion
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Hello, this is a test."}],
            max_tokens=10
        )
        
        print(f"✅ OpenAI connection successful!")
        print(f"   Response: {response.choices[0].message.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenAI connection failed: {e}")
        return False

def test_environment():
    """Test environment configuration"""
    print("🔧 Testing environment configuration...")
    
    # Check .env file
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check required variables
    required_vars = ["BROWSERBASE_PROJECT_ID", "BROWSERBASE_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ Environment configuration valid!")
    return True

def test_dependencies():
    """Test if all dependencies are installed"""
    print("📦 Testing dependencies...")
    
    required_packages = [
        "fastapi",
        "uvicorn",
        "websockets",
        "browserbase",
        "playwright",
        "openai",
        "python-dotenv",
        "pydantic"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("   Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies installed!")
    return True

async def main():
    """Main test function"""
    print("🧪 Autonomous Browser Agent Setup Test")
    print("=" * 50)
    
    tests = [
        ("Environment", test_environment),
        ("Dependencies", test_dependencies),
        ("Browserbase", test_browserbase_connection),
        ("Playwright", test_playwright_connection),
        ("OpenAI", test_openai_connection),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:15} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your setup is ready.")
        print("\nTo start the application, run:")
        print("  python start.py")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autonomous Browser Agent</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .control-panel {
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-bottom: 1rem;
        }

        .control-panel input {
            flex: 1;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
        }

        .control-panel input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 6px;
            background: #f7fafc;
            border-left: 4px solid #667eea;
        }

        .status.connected {
            background: #f0fff4;
            border-left-color: #48bb78;
        }

        .status.error {
            background: #fed7d7;
            border-left-color: #f56565;
        }

        .logs {
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e9ecef;
        }

        .log-entry {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .log-time {
            color: #6c757d;
            font-size: 0.8rem;
            min-width: 80px;
        }

        .log-type {
            font-weight: 600;
            min-width: 100px;
        }

        .log-message {
            flex: 1;
            word-break: break-word;
        }

        .log-system {
            background: #e3f2fd;
            border-left: 3px solid #2196f3;
        }

        .log-instruction {
            background: #f3e5f5;
            border-left: 3px solid #9c27b0;
        }

        .log-result {
            background: #e8f5e8;
            border-left: 3px solid #4caf50;
        }

        .log-error {
            background: #ffebee;
            border-left: 3px solid #f44336;
        }

        .log-reasoning {
            background: #fef5e7;
            border-left: 3px solid #f6ad55;
        }

        .browser-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
        }

        .browser-info p {
            margin: 0.5rem 0;
            font-size: 0.9rem;
        }

        .executing {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Autonomous Browser Agent</h1>
            <p>Control navegadores web con instrucciones en lenguaje natural</p>
        </div>

        <div class="card">
            <div id="status" class="status">
                <span>🔴 Desconectado</span>
            </div>

            <div class="control-panel">
                <button id="connectBtn" class="btn btn-primary" onclick="startSession()">
                    Conectar Agente
                </button>
            </div>

            <div id="instructionPanel" class="control-panel" style="display: none;">
                <input
                    type="text"
                    id="instructionInput"
                    placeholder="Ingresa una instrucción (ej: 'ir a google.com')"
                    onkeydown="handleKeyDown(event)"
                >
                <button id="sendBtn" class="btn btn-primary" onclick="sendInstruction()">
                    Enviar
                </button>
            </div>

            <div id="browserInfo" class="browser-info" style="display: none;">
                <p><strong>Session ID:</strong> <span id="sessionId">-</span></p>
                <p><strong>URL Actual:</strong> <span id="currentUrl">-</span></p>
                <p><strong>Título:</strong> <span id="pageTitle">-</span></p>
            </div>
        </div>

        <div class="card">
            <h3>📋 Registro de Actividad</h3>
            <div id="logs" class="logs">
                <div class="log-entry log-system">
                    <span class="log-time">--:--:--</span>
                    <span class="log-type">[Sistema]</span>
                    <span class="log-message">Listo para conectar...</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let sessionId = null;
        let connected = false;
        let isExecuting = false;

        function addLog(type, message) {
            const logs = document.getElementById('logs');
            const time = new Date().toLocaleTimeString();

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type.toLowerCase()}`;
            logEntry.innerHTML = `
                <span class="log-time">${time}</span>
                <span class="log-type">[${type}]</span>
                <span class="log-message">${message}</span>
            `;

            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
        }

        function updateStatus(status, isConnected = false, isError = false) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = status;
            statusEl.className = 'status';
            if (isConnected) statusEl.classList.add('connected');
            if (isError) statusEl.classList.add('error');
        }

        async function startSession() {
            try {
                updateStatus('🔄 Conectando...');
                document.getElementById('connectBtn').disabled = true;

                addLog('Sistema', 'Intentando conectar al backend...');

                // Primero verificar que el backend esté disponible
                try {
                    const healthResponse = await fetch('http://localhost:8000/health', {
                        method: 'GET',
                        mode: 'cors',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    });

                    if (!healthResponse.ok) {
                        throw new Error(`Backend no disponible: ${healthResponse.status}`);
                    }

                    addLog('Sistema', '✅ Backend disponible');
                } catch (healthError) {
                    throw new Error(`No se puede conectar al backend: ${healthError.message}`);
                }

                // Ahora crear la sesión
                addLog('Sistema', 'Creando sesión de navegador...');

                const response = await fetch('http://localhost:8000/start-session', {
                    method: 'POST',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Error del servidor: ${response.status} - ${errorText}`);
                }

                const data = await response.json();
                sessionId = data.session_id;

                document.getElementById('sessionId').textContent = sessionId;
                document.getElementById('browserInfo').style.display = 'block';

                addLog('Sistema', `✅ Sesión creada: ${sessionId}`);
                connectWebSocket(sessionId);

            } catch (error) {
                updateStatus('❌ Error de conexión', false, true);
                addLog('Error', `❌ ${error.message}`);
                addLog('Sistema', '💡 Verifica que el backend esté ejecutándose en http://localhost:8000');
                document.getElementById('connectBtn').disabled = false;
            }
        }

        function connectWebSocket(id) {
            addLog('Sistema', 'Conectando WebSocket...');

            try {
                ws = new WebSocket(`ws://localhost:8000/ws/${id}`);

                ws.onopen = () => {
                    connected = true;
                    updateStatus('🟢 Conectado', true);
                    addLog('Sistema', '✅ WebSocket conectado');

                    document.getElementById('connectBtn').style.display = 'none';
                    document.getElementById('instructionPanel').style.display = 'flex';
                };

                ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);

                        if (data.type === 'state_update') {
                            const state = data.data;
                            if (state.url) {
                                document.getElementById('currentUrl').textContent = state.url;
                            }
                            if (state.title) {
                                document.getElementById('pageTitle').textContent = state.title;
                            }
                        } else if (data.type === 'instruction_result') {
                            isExecuting = false;
                            updateSendButton();
                            const result = data.data;
                            if (result.status === 'success') {
                                addLog('Resultado', `✅ ${result.message || 'Instrucción completada'}`);
                            } else {
                                addLog('Error', `❌ ${result.message || 'Instrucción falló'}`);
                            }
                        } else if (data.type === 'reasoning_step') {
                            addLog('Razonamiento', `🤔 ${data.data.message}`);
                        } else if (data.type === 'instruction_received') {
                            isExecuting = true;
                            updateSendButton();
                            addLog('Sistema', `📝 Procesando: ${data.data.instruction}`);
                        } else if (data.type === 'connection_established') {
                            addLog('Sistema', '🔗 Conexión del navegador establecida');
                        } else if (data.type === 'error') {
                            isExecuting = false;
                            updateSendButton();
                            addLog('Error', `❌ ${data.message}`);
                        }
                    } catch (parseError) {
                        addLog('Error', `❌ Error parsing WebSocket message: ${parseError.message}`);
                    }
                };

                ws.onclose = (event) => {
                    connected = false;
                    updateStatus('🔴 Desconectado');
                    addLog('Sistema', `WebSocket desconectado (código: ${event.code})`);
                };

                ws.onerror = (error) => {
                    updateStatus('❌ Error de WebSocket', false, true);
                    addLog('Error', `❌ Error de WebSocket: ${error.message || 'Conexión fallida'}`);
                };

            } catch (wsError) {
                addLog('Error', `❌ Error creando WebSocket: ${wsError.message}`);
                updateStatus('❌ Error de conexión', false, true);
                document.getElementById('connectBtn').disabled = false;
            }
        }

        function sendInstruction() {
            const input = document.getElementById('instructionInput');
            const instruction = input.value.trim();

            if (!ws || !instruction || isExecuting) return;

            ws.send(JSON.stringify({ command: instruction }));
            addLog('Instrucción', `🚀 ${instruction}`);
            input.value = '';
        }

        function updateSendButton() {
            const btn = document.getElementById('sendBtn');
            const input = document.getElementById('instructionInput');

            if (isExecuting) {
                btn.textContent = 'Ejecutando...';
                btn.disabled = true;
                input.disabled = true;
                btn.classList.add('executing');
            } else {
                btn.textContent = 'Enviar';
                btn.disabled = false;
                input.disabled = false;
                btn.classList.remove('executing');
            }
        }

        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendInstruction();
            }
        }

        // Ejemplos de comandos
        addLog('Sistema', 'Ejemplos de comandos:');
        addLog('Sistema', '• "ir a google.com"');
        addLog('Sistema', '• "hacer clic en buscar"');
        addLog('Sistema', '• "escribir hola mundo en el campo de búsqueda"');
        addLog('Sistema', '• "desplazarse hacia abajo"');
    </script>
</body>
</html>
